import React, { useState } from "react";
import {
  Accordion,
  AccordionI<PERSON>,
  <PERSON>,
  CardBody,
  Card<PERSON><PERSON>er,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useQuery } from "@apollo/client";

import {
  AllFieldsDocument,
  AllRulesDocument,
  AllNotificationsDocument,
  FieldType,
  RuleType,
  NotificationType,
} from "@/graphql/schemas/generated";

interface TemplateData {
  name: string;
  description: string;
  type: string;
}

interface TemplateSummaryProps {
  templateData: TemplateData;
  selectedFields: string[];
  selectedRules: string[];
  selectedNotifications: string[];
  onCreateTemplate: () => void;
}

export default function TemplateSummary({
  templateData,
  selectedFields,
  selectedRules,
  selectedNotifications,
  onCreateTemplate,
}: TemplateSummaryProps) {
  const [isCreating, setIsCreating] = useState(false);

  const { data: fieldsData } = useQuery(AllFieldsDocument);
  const { data: rulesData } = useQuery(AllRulesDocument);
  const { data: notificationsData } = useQuery(AllNotificationsDocument);

  const fields = fieldsData?.allFields || [];
  const rules = rulesData?.allRules || [];
  const notifications = notificationsData?.allNotifications || [];

  const selectedFieldsData = fields.filter((field: FieldType) =>
    selectedFields.includes(field.id),
  );
  const selectedRulesData = rules.filter((rule: RuleType) =>
    selectedRules.includes(rule.id),
  );
  const selectedNotificationsData = notifications.filter(
    (notification: NotificationType) =>
      selectedNotifications.includes(notification.id),
  );

  const handleCreateTemplate = async () => {
    setIsCreating(true);
    try {
      await onCreateTemplate();
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Template Information */}
      <Card className="m-2 ps-1">
        <CardHeader className="pb-2">
          <div className="flex items-center gap-2">
            <Icon
              className="text-primary"
              icon="heroicons:document-text"
              width={20}
            />
            <h3 className="text-lg font-semibold">
              Información de la plantilla
            </h3>
          </div>
        </CardHeader>
        <CardBody className="pt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p className="text-sm font-medium text-default-600">Nombre</p>
              <p className="text-lg">{templateData.name}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-default-600">
                Descripción
              </p>
              <p className="text-lg">{templateData.description}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-default-600">Tipo base</p>
              <p className="text-lg capitalize">{templateData.type}</p>
            </div>
          </div>
        </CardBody>
      </Card>

      <Accordion
        className="w-full"
        defaultExpandedKeys={[
          "selected-fields",
          "selected-rules",
          "selected-notifications",
        ]}
        selectionMode="multiple"
        variant="splitted"
      >
        {/* Selected Fields */}
        <AccordionItem
          key="selected-fields"
          title={
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <Icon
                  className="text-primary"
                  icon="heroicons:squares-2x2"
                  width={20}
                />
                <h3 className="text-lg font-semibold">Campos seleccionados</h3>
              </div>
              <Chip color="primary" variant="flat">
                {selectedFields.length} campos
              </Chip>
            </div>
          }
          value="selected-fields"
        >
          {selectedFieldsData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {selectedFieldsData
                .sort((a: FieldType, b: FieldType) =>
                  a.subphase?.phase?.name.localeCompare(
                    b.subphase?.phase?.name,
                  ),
                )
                .sort((a: FieldType, b: FieldType) =>
                  a.subphase?.name.localeCompare(b.subphase?.name),
                )
                .sort((a: FieldType, b: FieldType) =>
                  a.name.localeCompare(b.name),
                )
                .map((field: FieldType) => (
                  <div
                    key={field.id}
                    className="p-3 border border-default-200 rounded-lg"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm">{field.name}</h4>
                      <Chip size="sm" variant="flat">
                        {field.type}
                      </Chip>
                    </div>
                    <p className="text-xs text-default-500 line-clamp-2">
                      {field.description || "Sin descripción"}
                    </p>
                    <div className="mt-2 flex gap-1">
                      <Chip className="text-xs" size="sm" variant="bordered">
                        {field.subphase?.phase?.name}
                      </Chip>
                      {field.isMilestone && (
                        <Chip
                          className="text-xs"
                          color="warning"
                          size="sm"
                          variant="flat"
                        >
                          Hito
                        </Chip>
                      )}
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <p className="text-default-500 text-center py-4">
              No se han seleccionado campos
            </p>
          )}
        </AccordionItem>

        <AccordionItem
          key="selected-rules"
          title={
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <Icon
                  className="text-warning"
                  icon="heroicons:cog-6-tooth"
                  width={20}
                />
                <h3 className="text-lg font-semibold">Reglas seleccionadas</h3>
              </div>
              <Chip color="warning" variant="flat">
                {selectedRules.length} reglas
              </Chip>
            </div>
          }
          value="selected-rules"
        >
          {/* Selected Rules */}
          {selectedRulesData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {selectedRulesData
                .sort((a: RuleType, b: RuleType) =>
                  a.name.localeCompare(b.name),
                )
                .map((rule: RuleType) => (
                  <div
                    key={rule.id}
                    className="p-3 border border-default-200 rounded-lg"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-sm">{rule.name}</h4>
                      <Chip size="sm" variant="flat">
                        {rule.status}
                      </Chip>
                    </div>
                    <p className="text-xs text-default-500 line-clamp-2 mb-2">
                      {rule.description || "Sin descripción"}
                    </p>
                    <div className="flex gap-1 flex-wrap">
                      <Chip className="text-xs" size="sm" variant="bordered">
                        {rule.action}
                      </Chip>
                      <Chip className="text-xs" size="sm" variant="bordered">
                        {rule.condition}
                      </Chip>
                    </div>
                  </div>
                ))}
            </div>
          ) : (
            <p className="text-default-500 text-center py-4">
              No se han seleccionado reglas
            </p>
          )}
        </AccordionItem>

        <AccordionItem
          key="selected-notifications"
          title={
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <Icon
                  className="text-secondary"
                  icon="heroicons:bell"
                  width={20}
                />
                <h3 className="text-lg font-semibold">
                  Notificaciones seleccionadas
                </h3>
              </div>
              <Chip color="secondary" variant="flat">
                {selectedNotifications.length} notificaciones
              </Chip>
            </div>
          }
          value="selected-notifications"
        >
          {/* Selected Notifications */}
          {selectedNotificationsData.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {selectedNotificationsData
                .sort((a: NotificationType, b: NotificationType) =>
                  a.name.localeCompare(b.name),
                )
                .map((notification: NotificationType) => (
                  <div
                    key={notification.id}
                    className="p-3 border border-default-200 rounded-lg"
                  >
                    <h4 className="font-medium text-sm mb-2">
                      {notification.name}
                    </h4>
                    <p className="text-xs text-default-500 line-clamp-3">
                      {notification.description || "Sin descripción"}
                    </p>
                  </div>
                ))}
            </div>
          ) : (
            <p className="text-default-500 text-center py-4">
              No se han seleccionado notificaciones
            </p>
          )}
        </AccordionItem>
      </Accordion>

      <Divider />

      {/* Create Button */}
      <div className="flex justify-center">
        <Button
          color="primary"
          isLoading={isCreating}
          size="md"
          onPress={handleCreateTemplate}
        >
          {isCreating ? "Creando plantilla..." : "Crear plantilla"}
        </Button>
      </div>
    </div>
  );
}
